#include "ASecLocalHostHelper.hpp"

#include <QWebSocket>
#include <QWebSocketServer>
#include <QVariant>
#include <QJsonDocument>
#include <QJsonObject>
#include <QApplication>
#include <QFile>
#include <QSslKey>
#include <QTimer>
#include <QMutexLocker>
#include <comdef.h>

#include "ASecBusinessCtrl.hpp"
#include "ASecCommonAPi.hpp"
#include "ASecAccountModel.hpp"
#include "ASecProtoc.pb.h"
#include "ASecConfigCtrl.hpp"
#include "ZLog.hpp"

#include "AsecCommonDefine.h"
#include "QAsecCommonLib.h"

#define WEBSOCKET_ACTION_KEY        "action"
#define WEBSOCKET_MSG_KEY           "msg"
#define WEBSOCKET_PLATFORM_KEY      "platform"

#define WEBSOCKET_COMPATIBLE_PORT    50001      // websocket兼容端口
#define WEBSOCKET_START_PORT         23256      // websocket新监听端口

bool ASecLocalHostHelper::init()
{
	bool bSucceed = false;
	ASEC_CONFIG_CTRL->getIntValue(COMMON_GROUP, "ipcport", &local_socket_port_);

	websocket_port_ = WEBSOCKET_COMPATIBLE_PORT;

	if(!tcp_socket_)
	{
		tcp_socket_ = new QTcpSocket(this);
		tcp_socket_->connectToHost(QHostAddress::LocalHost, local_socket_port_);
		connect(tcp_socket_, &QTcpSocket::connected, this, &ASecLocalHostHelper::slotConnected);
		connect(tcp_socket_, SIGNAL(error(QAbstractSocket::SocketError)), this, SLOT(slotConnectError(QAbstractSocket::SocketError)));
		connect(tcp_socket_, &QTcpSocket::readyRead, this, &ASecLocalHostHelper::slotReadyRead);
	}

	if(!web_server_)
	{
	#ifdef _WIN32
		web_server_ = new QWebSocketServer(QStringLiteral("Server"), QWebSocketServer::NonSecureMode, this);
	#endif
	#ifdef __APPLE__
		web_server_ = new QWebSocketServer(QStringLiteral("Server"), QWebSocketServer::NonSecureMode, this);

		//        QSslConfiguration sslConfiguration;
		//        QFile certFile(QCoreApplication::applicationDirPath() + "/cert.pem");
		//        QFile keyFile(QCoreApplication::applicationDirPath() + "/private.key");
		//
		//        certFile.open(QIODevice::ReadOnly);
		//        keyFile.open(QIODevice::ReadOnly);
		//
		//        QSslCertificate certificate(&certFile, QSsl::Pem);
		//        QSslKey sslKey(&keyFile, QSsl::Rsa, QSsl::Pem);
		//        certFile.close();
		//        keyFile.close();
		//
		//        if(certificate.isNull()) {
		//            LOG_F(INFO,"certficate is null");
		//        }
		//        if(sslKey.isNull()) {
		//            LOG_F(INFO,"sslKey is null");
		//        }
		//
		//        sslConfiguration.setPeerVerifyMode(QSslSocket::VerifyNone);
		//        sslConfiguration.setLocalCertificate(certificate);
		//        sslConfiguration.setPrivateKey(sslKey);
		//        m_pWebServer->setSslConfiguration(sslConfiguration);

	#endif

		connect(web_server_, &QWebSocketServer::newConnection, this, &ASecLocalHostHelper::slotWebSocketCommming);
		int increment_port = 18;
		int retry_count = 0;
		while(!bSucceed)
		{
			bSucceed = web_server_->listen(QHostAddress::LocalHost, websocket_port_);
			qDebug() << QString("listen websocket port %1 %2").arg(websocket_port_).arg(bSucceed ? "succeed" : "failed");

			if(!bSucceed)
			{
				if(websocket_port_ == WEBSOCKET_COMPATIBLE_PORT)
				{
					websocket_port_ = WEBSOCKET_START_PORT;
				}
				else
				{
					websocket_port_ += increment_port;
				}

				if(++retry_count > 50)
				{
					qCritical() << "try listen websocket over times, times:" << retry_count;
					break;
				}

				std::this_thread::sleep_for(std::chrono::milliseconds(50));
			}
		}
	}

	return true;
}

void ASecLocalHostHelper::unInit()
{
	if(web_server_)
	{
		web_server_->close();
		web_server_->deleteLater();
		web_server_ = nullptr;
	}

	if(tcp_socket_)
	{
		tcp_socket_->disconnectFromHost();
		tcp_socket_->waitForDisconnected(3000);
		tcp_socket_->deleteLater();
		tcp_socket_ = nullptr;
	}
}

int ASecLocalHostHelper::localHostPort()
{
	return local_socket_port_;
}

int ASecLocalHostHelper::websocketPort()
{
	return websocket_port_;
}

ASecLocalHostHelper::ASecLocalHostHelper()
	: tcp_socket_(nullptr)
	, web_server_(nullptr)
	, uninstall_service_mode_(false)
{
}

ASecLocalHostHelper::~ASecLocalHostHelper()
{
}

bool ASecLocalHostHelper::sendMsg(const ASecLocalHostDef::HostMessage& msg)
{
	QMutexLocker qMutexLocker(&m_qSendMsgMutex);

	if(!tcp_socket_ || tcp_socket_->state() != QAbstractSocket::ConnectedState)
	{
		qCritical("sendmsg failed, socket is invalid");
		return false;
	}

	std::string strMsg = "";
	if(msg.SerializeToString(&strMsg))
	{
		std::int32_t pkgLength = strMsg.length();

		char* szData = new char[pkgLength + sizeof(std::int32_t)];
		memset(szData, 0, pkgLength + sizeof(std::int32_t));
		memcpy(szData, &pkgLength, sizeof(std::int32_t));
		memcpy(szData + sizeof(std::int32_t), strMsg.c_str(), pkgLength);

		auto write_length = tcp_socket_->write(szData, pkgLength + sizeof(std::int32_t));
		delete szData;
		if(write_length != (pkgLength + sizeof(std::int32_t)))
		{
			return false;
		}
	}

	return true;
}

bool ASecLocalHostHelper::isSocketConnected()
{
	if(!tcp_socket_)
	{
		return false;
	}

	return tcp_socket_->state() == QAbstractSocket::ConnectedState ? true : false;
}

bool ASecLocalHostHelper::sendWsMsg(const QString& strMsg)
{
	for(const auto& itor : m_qSetWSClient)
	{
		itor->sendTextMessage(strMsg);
	}
	return true;
}

void ASecLocalHostHelper::setUninstallServiceMode(bool b)
{
	uninstall_service_mode_ = b;
}

void ASecLocalHostHelper::dealMsg(const ASecLocalHostDef::HostMessage& msg)
{
	qDebug().noquote() << QStringFrom("Tun信息，流程状态:%1, 原因:%2").arg(QString::number(msg.processstate()), QString::number(msg.reason()));
	// tun-cli状态刷新
	if(msg.processstate() > ASecLocalHostDef::HostMessage_State_UI_OFFLINE &&
		msg.processstate() < ASecLocalHostDef::HostMessage_State_SIDECAR_ONLINE)
	{
		if(ASEC_BUSINESS_CTRL->tunState() == ASecLocalHostDef::HostMessage_State_TUN_OFFLINE &&
			ASEC_BUSINESS_CTRL->tunState() == msg.processstate() &&
			msg.reason() == ASecLocalHostDef::HostMessage_Reason_DEFAULT_REASON)
		{
			qDebug().noquote() << QStringFrom("离线状态但是没有错误");
		}
		else
		{
			qDebug().noquote() << QStringFrom("更新状态并通知观察者");
			ASEC_BUSINESS_CTRL->updateTunState(msg.processstate(), msg.reason());
		}
	}

	// 退出ui主进程
	if(msg.action() == ASecLocalHostDef::HostMessage::ACT_SHUTDOWN_UI)
	{
		qApp->quit();
	}
}

void ASecLocalHostHelper::retryConnectServer()
{
	qDebug("start retry connect socket server");
	// 定时2秒重连
	QTimer::singleShot(2000, [&]()
	{
		if(tcp_socket_)
		{
			// 重试时每次重新去从配置文件读取ipc端口
			ASEC_CONFIG_CTRL->getIntValue(COMMON_GROUP, "ipcport", &local_socket_port_);
			tcp_socket_->connectToHost(QHostAddress::LocalHost, local_socket_port_);
		}
	});
}

bool ASecLocalHostHelper::verifyPlataddrAddress(const QString& plataddr_address)
{
	std::string login_plataddr = "";
	ASEC_CONFIG_CTRL->getStrValue(COMMON_GROUP, KEY_LOGIN_PLATADDR, &login_plataddr);
	if(login_plataddr.empty())
	{
		return false;
	}

	if(plataddr_address.toStdString() == login_plataddr)
	{
		return true;
	}

	return false;
}

void ASecLocalHostHelper::slotConnected()
{
	qDebug() << QString("connect to service %1 succeed").arg(local_socket_port_);

	ASecLocalHostDef::HostMessage msg;
	msg.set_hosttype(ASecLocalHostDef::HostMessage::HT_CLIENTANDUI);
	msg.set_processstate(ASecLocalHostDef::HostMessage::UI_ONLINE);

	// 通知服务端 Ui主进程已在线
	sendMsg(msg);

	emit sigConnected();
}

void ASecLocalHostHelper::slotConnectError(QAbstractSocket::SocketError error)
{
	qCritical() << QString("connect to service %1 failed, error code %2").arg(local_socket_port_).arg(error);

	// 如果界面连接状态，则刷新状态为未连接
	if(ASEC_BUSINESS_CTRL->tunState() == ASecLocalHostDef::HostMessage::TUN_ONLINE)
	{
		ASEC_BUSINESS_CTRL->updateTunState(ASecLocalHostDef::HostMessage::TUN_OFFLINE, ASecLocalHostDef::HostMessage_Reason_UNKNOW_REASON);
	}

	retryConnectServer();
}

void ASecLocalHostHelper::slotReadyRead()
{
	while(tcp_socket_ && tcp_socket_->bytesAvailable() > 0)
	{
		// 解析包头, 获取pkg大小 ,统一定义数据包前四个字节为包头 int32
		char* szHeader = new char[sizeof(std::int32_t)];
		memset(szHeader, 0, sizeof(std::int32_t));
		tcp_socket_->read(szHeader, sizeof(std::int32_t));
		std::int32_t nPackageLength = *(int*)szHeader;

		// 释放申请的包头空间
		delete szHeader;

		if(nPackageLength <= 0)
		{
			// 数据不合法
			continue;
		}

		// 根据包头获取的pkg大小读取pkg +1为字符串/0结尾符
		char* szData = new char[nPackageLength + 1];
		memset(szData, 0, nPackageLength + 1);
		tcp_socket_->read(szData, nPackageLength);

		ASecLocalHostDef::HostMessage deserializedMessage;
		if(!deserializedMessage.ParseFromString(szData))
		{
			continue;
		}
		// 释放申请的pkg空间大小
		delete szData;

		ASecLocalHostDef::HostMessage_HostType hostType = deserializedMessage.hosttype();

		// 如果不是已定义的进程间hostType则数据无效返回
		if(hostType != ASecLocalHostDef::HostMessage_HostType::HostMessage_HostType_HT_CLIENTANDUI)
		{
			return;
		}

		this->dealMsg(deserializedMessage);
	}
}

void ASecLocalHostHelper::slotWebSocketCommming()
{
	QWebSocket* pSocket = web_server_->nextPendingConnection();

	if(!pSocket)
	{
		return;
	}

	//将WS客户端连接保存到set中，用于后续发送数据
	m_qSetWSClient.insert(pSocket);

	connect(pSocket, &QWebSocket::textMessageReceived,
		this, &ASecLocalHostHelper::slotWebSocketMsgRead);

	connect(pSocket, &QWebSocket::disconnected,
		this, &ASecLocalHostHelper::slotWebSocketDisConnect);
}

void ASecLocalHostHelper::slotWebSocketMsgRead(const QString& strMsg)
{
	qDebug().noquote() << "Recieved WSClient Msg:" << strMsg;

	QVariant qVar;
	bool bRet = false;

	WebSocketMsgAction enumAct = kUnknown;
	bRet = ASecCommonAPi::getDataFromJson(strMsg, WEBSOCKET_ACTION_KEY, qVar);
	if(bRet && qVar.isValid())
	{
		enumAct = (WebSocketMsgAction)qVar.toInt();
	}

	QString strDataMsg;
	bRet = ASecCommonAPi::getDataFromJson(strMsg, WEBSOCKET_MSG_KEY, qVar);
	if(bRet && qVar.isValid())
	{
		strDataMsg = QJsonDocument::fromVariant(qVar).toJson();
	}

	QString strPlatform;
	bRet = ASecCommonAPi::getDataFromJson(strMsg, WEBSOCKET_PLATFORM_KEY, qVar);
	if(bRet && qVar.isValid())
	{
		strPlatform = qVar.toString();
	}

	auto pSocket = qobject_cast<QWebSocket*>(sender());
	// socket不合法，直接return
	if(!pSocket)
	{
		return;
	}

	// 校验ip是否是当前客户端使用的ip(还是做个platform不为空的判断吧，兼容一下老平台)
	if(!strPlatform.isEmpty() && !verifyPlataddrAddress(strPlatform))
	{
		return;
	}

	switch(enumAct)
	{
		case kToken:
		// 当消息不为空并且tun处于未连接状态时执行保存token操作，否则token的刷新交由tun管理
		if(!strDataMsg.isEmpty() && ASEC_BUSINESS_CTRL->tunState() == ASecLocalHostDef::HostMessage::TUN_OFFLINE)
		{
			// web唤醒带token
			if(ASEC_ACCOUNT_MODEL->saveToken(strDataMsg))
			{
				// 保存token成功,拉起tun
				if(ASEC_ACCOUNT_MODEL->checkToken())
				{
					ASecLocalHostDef::HostMessage msg;
					msg.set_hosttype(ASecLocalHostDef::HostMessage::HT_CLIENTANDUI);
					msg.set_action(ASecLocalHostDef::HostMessage::ACT_START_TUN);
					if(ASEC_LOCALHOST_HELPER->sendMsg(msg))
					{
						// 通知sidecar读数据库时手动将tunState置为连接中状态
						ASEC_BUSINESS_CTRL->updateTunState(ASecLocalHostDef::HostMessage::TUN_CONNECTING);
					}
				}
			}
		}
		// 转发给所有其他websocket客户端
		for(const auto& client : m_qSetWSClient)
		{
			if(client != pSocket)
			{
				client->sendTextMessage(strMsg);
			}
		}
		emit sigWebAuthed();
		break;

		case kLogOut:
		// 如果tun-cli是连接状态，则断开连接
		if(ASEC_BUSINESS_CTRL->tunState() == ASecLocalHostDef::HostMessage::TUN_ONLINE)
		{
			ASEC_BUSINESS_CTRL->disconnect();
		}
		// 删除本地toke.json 注销登录
		if(ASEC_ACCOUNT_MODEL->isLogined())
		{
			ASEC_ACCOUNT_MODEL->logout();
		}
		// 转发给所有其他websocket客户端
		for(const auto& client : m_qSetWSClient)
		{
			if(client != pSocket)
			{
				client->sendTextMessage(strMsg);
			}
		}
		emit sigWebLogOut();
		break;

		case kQueryToken:
		if(ASEC_ACCOUNT_MODEL->isLogined())
		{
			QByteArray token = ASEC_ACCOUNT_MODEL->getToken();
			auto doc = QJsonDocument::fromJson(token);

			QJsonObject json_obj;
			json_obj["action"] = kToken;
			if(ASEC_ACCOUNT_MODEL->isLogined() && ASEC_ACCOUNT_MODEL->isTokenValid())
			{
				QByteArray token = ASEC_ACCOUNT_MODEL->getToken();
				auto doc = QJsonDocument::fromJson(token);
				json_obj["msg"] = doc.toVariant().toJsonObject();
			}
			else
			{
				json_obj["msg"] = "";
			}

			QJsonDocument json_doc(json_obj);
			QByteArray json_string = json_doc.toJson(QJsonDocument::Compact);

			pSocket->sendTextMessage(json_string);
		} else {
			
			// 转发给所有其他websocket客户端
			for(const auto& client : m_qSetWSClient)
			{
				if(client != pSocket)
				{
					client->sendTextMessage(strMsg);
				}
			}
		}
		break;

		case kOpenApplication:  // 新增处理类型
		{
			QString appPath = getAppPathFromRegistry("CloudDeep Enterplorer");
			if(appPath.isEmpty())
			{
				qCritical("Failed to get Enterplorer application path");
				QJsonObject qJsonData;
				qJsonData["ErrorMsg"] = "Enterplorer path not found";
				pSocket->sendTextMessage(QAsecCommonLib::BuildJsonToWeb("Response", "False", qJsonData));
				break;
			}

			QProcess* process = new QProcess(this);
			QString workingDir = QFileInfo(appPath).absolutePath();

			// 使用QStringList来正确处理参数
			QStringList arguments;
			qint64 pid;  // 用于存储启动的进程ID

			// 记录详细的启动参数
			qDebug() << QString("Launching process with: Path=%1, WorkingDir=%2").arg(appPath, workingDir);

			// 使用重载版本，可以获取更多信息
			bool success = QProcess::startDetached(
				appPath,           // 程序路径
				arguments,         // 参数列表
				workingDir,       // 工作目录
				&pid              // 进程ID
			);

			if(success)
			{
				qDebug() << QString("Successfully opened Enterplorer: %1, PID: %2").arg(appPath, QString::number(pid));
				pSocket->sendTextMessage(QAsecCommonLib::BuildJsonToWeb("Response", "Success", QJsonObject()));
			}
			else
			{
				// 获取系统错误码
				DWORD errorCode = GetLastError();
				qCritical() << QString("Failed to open Enterplorer: %1, System Error Code: %2 (%3)").arg(appPath, QString::number(errorCode), QString::fromWCharArray(_com_error(errorCode).ErrorMessage()));
				QJsonObject qJsonData;
				qJsonData["ErrorMsg"] = "Could not start Enterplorer";
				pSocket->sendTextMessage(QAsecCommonLib::BuildJsonToWeb("Response", "False", qJsonData));
			}

			process->deleteLater();
		}
		break;

		case kOpenClientUI:
		{
			emit sigInvokeUI();
		}
		break;

		default:
		break;
	}
}

void ASecLocalHostHelper::slotWebSocketDisConnect()
{
	if(auto pSocket = qobject_cast<QWebSocket*>(sender()))
	{
		m_qSetWSClient.remove(pSocket);

		pSocket->abort();
		pSocket->deleteLater();
	}
}

QTcpSocket* ASecLocalHostHelper::GetTcpSocket()
{
	return tcp_socket_;
}

QString ASecLocalHostHelper::getAppPathFromRegistry(const QString& appName)
{
	QString appPath;
	ZRegEdit reg_edit;
	ZString install_path;

	// 定义可能的注册表路径
	const wchar_t* regPaths[] = {
		L"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\CloudDeep Enterplorer",        // 64位系统
		L"SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\CloudDeep Enterplorer"  // 32位程序在64位系统
	};

	for(const auto& basePath : regPaths)
	{
		if(reg_edit.OpenDir(basePath, false, HKEY_LOCAL_MACHINE, KEY_READ))
		{
			// 获取安装路径
			if(reg_edit.GetStr(L"InstallLocation", install_path))
			{
				// 构造完整的可执行文件路径
				appPath = QString::fromStdWString(install_path.GetWstr()) + "\\Enterplorer.exe";

				if(QFile::exists(appPath))
				{
					qDebug() << "Found Enterplorer at: " << appPath.toStdString().c_str();
					return appPath;
				}
			}
		}
	}

	qCritical("Failed to find Enterplorer installation path in registry");
	return QString();
}
